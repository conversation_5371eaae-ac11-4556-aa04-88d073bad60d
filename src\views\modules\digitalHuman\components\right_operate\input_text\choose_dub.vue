<template>
    <div class="right_operate_drive_text_captions_choose_dub">
        <div class="right_operate_drive_text_captions_choose_dub_operate">
            <!-- 水平布局容器：配音文字、Switch开关、定制声音按钮 -->
            <div class="voice-control-header">
                <div class="voice-control-title">
                    配音
                </div>
                <div class="voice-control-switch">
                    <el-switch 
                        v-model="useCustomCloneVoice" 
                        :active-text="'使用原声'"
                        :inactive-text="'使用原声'"
                        :active-color="'#0AAF60'"
                        inline-prompt
                        size="default"
                    />
                </div>
                <div class="voice-control-button">
                    <div class="custom-voice-button" @click="goToVoiceClone">
                        定制声音
                    </div>
                </div>
            </div>
            <div class="right_operate_drive_text_captions_choose_dub_character"
                 @click="!isVoiceSelectionDisabled && choose_character()"
                 :class="{ 'disabled': isVoiceSelectionDisabled }"
                 v-if="current_character">
                <div class="right_operate_drive_text_captions_choose_dub_character_img">
                    <img :src="current_character.info.avatarUrl" alt="" v-if="current_character.info.avatarUrl!=''">
                </div>
                 <div class="right_operate_drive_text_captions_choose_dub_character_text">
                    <h4>{{ current_character.info.platformNickname }}</h4>
                    <span>{{ current_character.info.emotionTags||current_character.info.sceneCategory }}</span>
                </div>
                 <div class="right_operate_drive_text_captions_choose_dub_character_more">
                    <img src="@/assets/images/digitalHuman/right_operate_drive_text_captions_choose_dub_character_more.svg" alt="">
                </div>
            </div>
            <div class="right_operate_drive_text_captions_choose_dub_character_empty"
                 @click="!isVoiceSelectionDisabled && choose_character()"
                 :class="{ 'disabled': isVoiceSelectionDisabled }"
                 v-else>
                <div class="right_operate_drive_text_captions_choose_dub_character_empty_img">
                    <img src="@/assets/images/digitalHuman/right_operate_add.svg" alt="">
                </div>
                <span>选择高级音色</span>
            </div>
            <div class="right_operate_drive_text_captions_choose_dub_adjust">
                <div class="right_operate_drive_text_captions_choose_dub_adjust_item">
                    <span class="right_operate_drive_text_captions_choose_dub_adjust_item_label">语调</span>
                    <el-slider v-model="intonation" :min="-12" :max="12" :step="1" show-input :show-tooltip="false"/>
                </div>
                <div class="right_operate_drive_text_captions_choose_dub_adjust_item">
                    <span class="right_operate_drive_text_captions_choose_dub_adjust_item_label">语速</span>
                    <el-slider v-model="speech" :min="0.5" :max="2.0" :step="0.01" show-input :show-tooltip="false"/>
                </div>
                <div class="right_operate_drive_text_captions_choose_dub_adjust_item">
                    <span class="right_operate_drive_text_captions_choose_dub_adjust_item_label">音量</span>
                    <el-slider v-model="volume" :min="0" :max="100" :step="1" show-input :show-tooltip="false"/>
                </div>
            </div>
        </div>
        <dubbingSelection ref="dubbing_selection_ref" @submit="set_character" :apiMap="apiMap"></dubbingSelection>
    </div>
</template>
<script setup>
import { ref, defineExpose, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useSoundStore } from '@/stores/modules/soundStore.js'
import dubbingSelection from '@/views/modules/digitalHuman/components/right_operate/input_text/dubbing_selection/index.vue'
import { queryUserBuyVoiceName, queryUserUsedVoiceName, cloneList } from '@/api_my/AlDubb'
import { bookmarkList } from '@/api/soundStore.js'
import { getAll } from '@/api/soundStore.js'
import { useloginStore } from '@/stores/login'
let loginStore = useloginStore()
let router = useRouter()
let route = useRoute()
let soundStore = useSoundStore()
let intonation=ref(0)
let speech=ref(1)
let volume=ref(100)
let dubbing_selection_ref=ref(null)
let current_character=ref(null)
let userId = ref(loginStore.userId)
let tts = ref(4)

// 新增：声音控制相关状态
let useCustomCloneVoice = ref(false) // Switch开关状态：是否使用数字人定制时克隆的声音

// 计算属性：判断"选择高级音色"按钮是否应该被禁用
const isVoiceSelectionDisabled = computed(() => {
    return useCustomCloneVoice.value // 当Switch开启时，禁用按钮
})
let choose_character=()=>{
    dubbing_selection_ref.value.dialogVisible=true
    console.log(current_character.value,'current_character');
    dubbing_selection_ref.value.init(current_character.value)
}

let set_character=(data)=>{
    current_character.value=data
}

// 新增：在新标签页中打开声音克隆页面的方法
const goToVoiceClone = () => {
    // 使用router.resolve获取完整的URL路径
    const routeData = router.resolve('/VoiceClone')
    // 在新标签页中打开声音克隆页面
    window.open(routeData.href, '_blank')
}
// 用ref包裹接口映射对象，确保响应式
let apiMap = ref({
  '已购': { fn: queryUserBuyVoiceName, params: { userId: userId.value, tts: tts.value } },
  '收藏': { fn: bookmarkList, params: { userId: userId.value, tts: tts.value } },
  '历史': { fn: queryUserUsedVoiceName, params: { userId: userId.value, tts: tts.value } },
  '克隆音色': { fn: cloneList, params: { userId: userId.value } },
  '全部音色': { fn: getAll, params: { userId: userId.value, tts: tts.value } },
})

// 克隆音色自动回显功能
const handleCloneVoiceAutoSelect = async () => {
    try {
        // 检测是否来自克隆页面
        if (route.query.clone !== 'true') {
            return
        }

        // 检测soundStore中是否有克隆数据
        const cloneData = soundStore.cloneData
        if (!cloneData || !cloneData.id || !cloneData.platformNickname) {
            console.warn('克隆音色数据不完整，跳过自动回显')
            return
        }

        console.log('检测到克隆音色数据，开始自动回显:', cloneData)

        // 等待组件完全挂载
        await nextTick()

        // 将克隆数据转换为current_character所需的格式
        const characterData = {
            character_id: cloneData.id,
            option_active: 2, // 我的音色
            selectetMycurrent: '克隆音色',
            info: {
                id: cloneData.id,
                avatarUrl: cloneData.avatarUrl || '',
                platformNickname: cloneData.platformNickname,
                voiceName: cloneData.platformNickname,
                // 保留其他可能的字段
                ...cloneData
            }
        }

        // 调用现有的设置方法完成回显
        set_character(characterData)

        // 清空soundStore中的克隆数据，避免重复使用
        soundStore.setCloneData(null)

        console.log('克隆音色自动回显完成:', characterData)

    } catch (error) {
        console.error('克隆音色自动回显失败:', error)
        // 出错时也要清空数据，避免影响后续操作
        soundStore.setCloneData(null)
    }
}

// 组件挂载时执行克隆音色自动回显
onMounted(() => {
    handleCloneVoiceAutoSelect()
})

defineExpose({
    current_character,
    intonation,
    speech,
    volume
})
</script>
<style lang="scss" scoped>
.right_operate_drive_text_captions_choose_dub{
    width: 100%;
    padding: 12px 18px ;
    flex: 1;
    display: flex;
    flex-direction: column;
    .right_operate_drive_text_captions_choose_dub_operate{
        display: flex;
        flex-direction: column;
        // 新增：水平布局容器样式
        .voice-control-header {
            display: flex;
            align-items: center;
            
            .voice-control-title {
                font-size: 14px;
                line-height: 42px;
                color: #000000;
                margin-right: 8px; // 与Switch间距8px
            }
            
            .voice-control-switch {
                flex: 1; // 占据中间空间
                display: flex;
                align-items: center;
                
                .el-switch {
                    ::v-deep(.el-switch__label) {
                        font-size: 12px;
                        color: rgba(0, 0, 0, 0.65);
                    }
                    
                    ::v-deep(.el-switch__core) {
                        background-color: #D6D6D6;
                        border-color: #D6D6D6;
                        
                        &.is-checked {
                            background-color: #0AAF60 !important;
                            border-color: #0AAF60 !important;
                        }
                    }
                    
                    // 确保Switch开启状态的颜色强制生效
                    &.is-checked {
                        ::v-deep(.el-switch__core) {
                            background-color: #0AAF60 !important;
                            border-color: #0AAF60 !important;
                        }
                    }
                }
            }
            
            .voice-control-button {
                margin-left: auto; // 推到右侧
                
                .custom-voice-button {
                    display: inline-block;
                    width: 66px;
                    height: 26px;
                    padding: 0;
                    background: transparent;
                    color: #303133;
                    border: 1px solid #D3D3D2;
                    border-radius: 3px;
                    font-size: 13px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    text-align: center;
                    line-height: 24px; // 26px高度减去2px边框 = 24px行高实现垂直居中
                    white-space: nowrap; // 防止文字换行
                    box-sizing: border-box; // 确保边框包含在尺寸内

                    &:hover {
                        background: #f5f5f5;
                        color: #303133;
                    }
                }
            }
        }
        .right_operate_drive_text_captions_choose_dub_character{
            display: flex;
            align-items: center;
            padding:13px 5px 13px 8px;
            width: 100%;
            background: #F1F2F4;
            border-radius: 4px;
            margin-bottom: 20px;
            cursor: pointer;
            
            // 新增：禁用状态样式
            &.disabled {
                opacity: 0.5;
                cursor: not-allowed;
                background: #f5f5f5;
            }
            
            .right_operate_drive_text_captions_choose_dub_character_img{
                width: 52px;
                height: 52px;
                border-radius: 50%;
                overflow: hidden;
                margin-right: 10px;
                background-color: #fff;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            .right_operate_drive_text_captions_choose_dub_character_text{
                display: flex;
                flex-direction: column;
                h4{
                    font-size: 14px;
                    line-height: 18px;
                    color: #1B2337;
                    margin: 0;
                    margin-bottom: 5px;
                    font-weight: normal;
                }
                span{
                    font-size: 12px;
                    line-height: 18px;
                    color: rgba(0, 0, 0, 0.45);
                }
            }
            .right_operate_drive_text_captions_choose_dub_character_more{
                margin-left: auto;
                width: 20px;
                height: 20px;
                img{    
                    width: 100%;
                    height: 100%;
                }
                }
            }
            .right_operate_drive_text_captions_choose_dub_character_empty{
                display: flex;
                align-items: center;
                justify-content: center;
                box-sizing: border-box;
                padding: 8px;
                width:100%;
                background: #F6F7FB;
                border: 1px dashed rgba(0, 0, 0, 0.1);
                border-radius: 4px;
                margin-bottom: 20px;
                cursor: pointer;
                
                // 新增：禁用状态样式
                &.disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                    background: #f0f0f0;
                    border-color: rgba(0, 0, 0, 0.05);
                }
                
                .right_operate_drive_text_captions_choose_dub_character_empty_img{
                    width: 26px;
                    height: 26px;
                    margin-right: 6px;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                span{
                    font-size: 14px;
                    line-height: 22px;
                    color: #858587;
                }
            }
            .right_operate_drive_text_captions_choose_dub_adjust{
                display: flex;
                flex-direction: column;
                .right_operate_drive_text_captions_choose_dub_adjust_item{
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;
                    .right_operate_drive_text_captions_choose_dub_adjust_item_label{
                        margin-right: 20px;
                        font-size: 14px;
                        line-height: 24px;
                        color: rgba(0, 0, 0, 0.45);

                    }
                    .el-slider{
                        flex: 1;
                        ::v-deep(.el-slider__runway){
                            background-color: #D6D6D6;
                            margin-right: 20px;
                            height: 2px;
                            .el-slider__bar{
                                background-color: #0AAF60;
                                height: 100%;
                            }
                            .el-slider__button-wrapper{
                                width: 8px;
                                height: 8px;
                                top: 50%;
                                transform: translateY(-50%);
                                .el-slider__button{
                                    width: 8px;
                                    height: 8px;
                                    background-color: #0AAF60;
                                    border-radius: 50%;
                                    border: none;
                                }
                            }
                        }
                        ::v-deep(.el-input-number){
                        width: 75px;
                        height: 18px;
                        line-height: 18px;
                        display: flex;
                        align-items: center;
                        .el-input-number__decrease,.el-input-number__increase{
                            width: 20px;
                            line-height: 18px;
                            background-color: transparent;
                            border: none;
                            .el-icon{
                                background-repeat: no-repeat;
                                background-position: center center;
                                line-height: 18px;
                                width: 8px;
                                svg{
                                    display: none;
                                }
                            }
                        }
                        .el-input-number__decrease{
                            .el-icon{
                                background-image: url('@/assets/images/digitalHuman/right_operate_drive_text_captions_choose_dub_reduce.svg');
                                background-size: 8px 1px;
                            }
                        }
                        .el-input-number__increase{
                            margin-left: auto;
                            .el-icon{
                                background-image: url('@/assets/images/digitalHuman/right_operate_drive_text_captions_choose_dub_increase.svg');
                                background-size: 8px 8px;
                            }
                        }
                        .el-input{
                            flex: 1;
                            width: 75px;
                            height: 100%;
                            line-height: 18px;
                            .el-input__wrapper{
                                padding: 0;
                                .el-input__inner{
                                    height: 18px;
                                    line-height: 18px ;
                                    font-size: 12px;
                                    color: #040404;
                                }
                            }
                        }
                        
                    }
                    }
                 
                    &:last-child{
                        margin-bottom: 0;
                    }
                }
            }
        }
}
</style>